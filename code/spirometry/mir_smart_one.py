import asyncio
from bleak import BleakClient, BleakScanner
import struct
import time


class MirSmartOne:
    def __init__(self, age=None, height_cm=None, gender=None, weight_kg=None, debug=False):
        self.device_address = "CF:C3:A0:A2:BE:15"  # SO-008-A043160
        self.client = None
        self.debug = debug  # Enable/disable verbose BLE logging

        # Patient demographics for predicted value calculations
        self.age = age
        self.height_cm = height_cm
        self.gender = gender  # 'male' or 'female'
        self.weight_kg = weight_kg

        # Main service UUID for the MIR Smart One
        self.service_uuid = "7f04f3f0-b665-11e3-a5e2-0800200c9a66"

        # Characteristic UUIDs based on observed values
        self.command_char = "92b403f0-b665-11e3-a5e2-0800200c9a66"
        self.fev1_char = "f9f84150-b667-11e3-a5e2-0800200c9a66"
        self.fvc_char = "f9f84151-b667-11e3-a5e2-0800200c9a66"
        self.fev1_fvc_char = "f9f84152-b667-11e3-a5e2-0800200c9a66"
        self.pef_char = "f9f84153-b667-11e3-a5e2-0800200c9a66"
        self.status_char = "7d32c0f0-bef5-11e3-b1b6-0800200c9a66"
        self.device_info_char = "2d417c80-b667-11e3-a5e2-0800200c9a66"
        self.battery_char = "1dcec130-b668-11e3-a5e2-0800200c9a66"

        # Standard battery service
        self.std_battery_char = "00002a19-0000-1000-8000-00805f9b34fb"

        # Test data storage
        self.fev1_value = 0
        self.fvc_value = 0
        self.pef_value = 0
        self.ratio_value = 0
        self.test_complete = False

        # Dynamic test detection
        self.test_started = False
        self.last_value_update = None
        self.value_stable_count = 0
        self.previous_values = {'fev1': 0, 'fvc': 0, 'pef': 0, 'ratio': 0}
        self.value_change_threshold = 0.01  # Minimum change to consider significant

        # Blow duration tracking
        self.blow_start_time = None
        self.blow_end_time = None

    async def connect(self):
        """Connect to the MIR Smart One device."""
        print(f"Scanning for MIR Smart One device: {self.device_address}")
        device = await BleakScanner.find_device_by_address(self.device_address)

        if not device:
            raise Exception(
                f"Could not find MIR Smart One device with address {self.device_address}")

        print(f"Connecting to {device.name or 'MIR Smart One'}")
        self.client = BleakClient(device)
        await self.client.connect()
        print("Connected!")

        # Print services for debugging
        await self.print_services()

    async def print_services(self):
        """Print all services and characteristics for debugging."""
        print("Discovering services...")
        for service in self.client.services:
            print(f"Service: {service.uuid}")
            for char in service.characteristics:
                print(
                    f"  Characteristic: {char.uuid}, Properties: {char.properties}")

    def _check_value_change(self, value_type, new_value):
        """Check if a value has changed significantly and update test state."""
        old_value = self.previous_values[value_type]
        if abs(new_value - old_value) > self.value_change_threshold:
            if not self.test_started and new_value > 0:
                self.test_started = True
                self.blow_start_time = time.time()
                print(
                    f"🫁 Test started detected - {value_type} changed from {old_value} to {new_value}")
            self.last_value_update = time.time()
            self.value_stable_count = 0
            self.previous_values[value_type] = new_value
        else:
            self.value_stable_count += 1

    def fev1_handler(self, sender, data):
        """Handle FEV1 notifications."""
        try:
            # Always print raw data for debugging
            print(f"[DEBUG] FEV1 raw data: {data.hex()} ({len(data)} bytes)")

            # Try multiple decoding approaches
            if len(data) == 4:
                # Try as float
                float_val = struct.unpack('<f', data)[0]
                print(f"[DEBUG] FEV1 as float: {float_val}")

                # Try as 32-bit int with different scales
                int_val = struct.unpack('<I', data)[0]
                print(
                    f"[DEBUG] FEV1 as uint32: {int_val} (scaled /1000: {int_val/1000}, /100: {int_val/100})")

                self.fev1_value = float_val
                print(f"📊 FEV1: {self.fev1_value} L")
                self._check_value_change('fev1', self.fev1_value)

            elif len(data) == 2:
                # Try as 16-bit int with different scales
                int_val = struct.unpack('<H', data)[0]
                print(f"[DEBUG] FEV1 as uint16: {int_val} (hex: {data.hex()})")
                print(
                    f"[DEBUG] Scaled options: /1000={int_val/1000}, /100={int_val/100}, /10={int_val/10}")

                if data.hex() == "ffff":
                    print("[DEBUG] FEV1 received error signal (0xFFFF)")
                    # Don't update value, keep previous or estimate
                    if self.fev1_value == 0 and self.fvc_value > 0 and self.ratio_value > 0:
                        self.fev1_value = (
                            self.fvc_value * self.ratio_value) / 100.0
                        print(
                            f"[DEBUG] FEV1 estimated from FVC*ratio: {self.fev1_value} L")
                    elif self.fev1_value == 0:
                        self.fev1_value = 0.05  # Fallback
                        print(
                            f"[DEBUG] FEV1 using fallback: {self.fev1_value} L")
                else:
                    # Try different scaling factors
                    self.fev1_value = int_val / 100.0  # Start with /100
                    print(f"📊 FEV1: {self.fev1_value} L (scaled /100)")
                    self._check_value_change('fev1', self.fev1_value)

            elif len(data) == 1:
                # Single byte value
                byte_val = data[0]
                print(f"[DEBUG] FEV1 as byte: {byte_val}")
                print(
                    f"[DEBUG] Scaled options: /100={byte_val/100}, /10={byte_val/10}, raw={byte_val}")

                self.fev1_value = byte_val / 10.0  # Try /10 instead of /100
                print(f"📊 FEV1: {self.fev1_value} L (scaled /10)")
                self._check_value_change('fev1', self.fev1_value)
            else:
                print(
                    f"[DEBUG] Unexpected FEV1 data format: {data.hex()} ({len(data)} bytes)")
        except Exception as e:
            print(f"Error decoding FEV1: {e}")

    def fvc_handler(self, sender, data):
        """Handle FVC notifications."""
        try:
            # Always print raw data for debugging
            print(f"[DEBUG] FVC raw data: {data.hex()} ({len(data)} bytes)")

            if len(data) == 4:
                float_val = struct.unpack('<f', data)[0]
                print(f"[DEBUG] FVC as float: {float_val}")
                self.fvc_value = float_val
                print(f"📊 FVC: {self.fvc_value} L")
                self._check_value_change('fvc', self.fvc_value)
            elif len(data) == 2:
                int_val = struct.unpack('<H', data)[0]
                print(f"[DEBUG] FVC as uint16: {int_val} (hex: {data.hex()})")
                print(
                    f"[DEBUG] Scaled options: /1000={int_val/1000}, /100={int_val/100}, /10={int_val/10}")
                self.fvc_value = int_val / 100.0  # Try /100 scaling
                print(f"📊 FVC: {self.fvc_value} L (scaled /100)")
                self._check_value_change('fvc', self.fvc_value)
            elif len(data) == 1:
                byte_val = data[0]
                print(f"[DEBUG] FVC as byte: {byte_val}")
                print(
                    f"[DEBUG] Scaled options: /100={byte_val/100}, /10={byte_val/10}, raw={byte_val}")
                self.fvc_value = byte_val / 10.0  # Try /10 scaling
                print(f"📊 FVC: {self.fvc_value} L (scaled /10)")
                self._check_value_change('fvc', self.fvc_value)
            else:
                print(
                    f"[DEBUG] Unexpected FVC data format: {data.hex()} ({len(data)} bytes)")
        except Exception as e:
            print(f"Error decoding FVC: {e}")

    def pef_handler(self, sender, data):
        """Handle PEF notifications."""
        try:
            # Always print raw data for debugging
            print(f"[DEBUG] PEF raw data: {data.hex()} ({len(data)} bytes)")

            if len(data) == 4:
                float_val = struct.unpack('<f', data)[0]
                print(f"[DEBUG] PEF as float: {float_val}")
                self.pef_value = float_val
                print(f"📊 PEF: {self.pef_value} L/s")
                self._check_value_change('pef', self.pef_value)
            elif len(data) == 2:
                int_val = struct.unpack('<H', data)[0]
                print(f"[DEBUG] PEF as uint16: {int_val} (hex: {data.hex()})")
                print(
                    f"[DEBUG] Scaled options: /1000={int_val/1000}, /100={int_val/100}, /10={int_val/10}")
                self.pef_value = int_val / 10.0  # PEF might need different scaling
                print(f"📊 PEF: {self.pef_value} L/s (scaled /10)")
                self._check_value_change('pef', self.pef_value)
            elif len(data) == 1:
                byte_val = data[0]
                print(f"[DEBUG] PEF as byte: {byte_val}")
                print(
                    f"[DEBUG] Scaled options: /100={byte_val/100}, /10={byte_val/10}, raw={byte_val}")
                self.pef_value = byte_val / 1.0  # Try raw value for PEF
                print(f"📊 PEF: {self.pef_value} L/s (raw)")
                self._check_value_change('pef', self.pef_value)
            else:
                print(
                    f"[DEBUG] Unexpected PEF data format: {data.hex()} ({len(data)} bytes)")
        except Exception as e:
            print(f"Error decoding PEF: {e}")

    def ratio_handler(self, sender, data):
        """Handle FEV1/FVC ratio notifications."""
        try:
            # Always print raw data for debugging
            print(
                f"[DEBUG] FEV1/FVC raw data: {data.hex()} ({len(data)} bytes)")

            if len(data) == 4:
                float_val = struct.unpack('<f', data)[0]
                print(f"[DEBUG] FEV1/FVC as float: {float_val}")
                self.ratio_value = float_val
                print(f"📊 FEV1/FVC: {self.ratio_value}%")
                self._check_value_change('ratio', self.ratio_value)
            elif len(data) == 2:
                int_val = struct.unpack('<H', data)[0]
                print(
                    f"[DEBUG] FEV1/FVC as uint16: {int_val} (hex: {data.hex()})")
                print(
                    f"[DEBUG] Scaled options: raw={int_val}, /10={int_val/10}, /100={int_val/100}")
                self.ratio_value = int_val  # Ratio might be direct percentage
                print(f"📊 FEV1/FVC: {self.ratio_value}% (raw)")
                self._check_value_change('ratio', self.ratio_value)
            elif len(data) == 1:
                byte_val = data[0]
                print(f"[DEBUG] FEV1/FVC as byte: {byte_val}")
                self.ratio_value = byte_val  # Direct percentage
                print(f"📊 FEV1/FVC: {self.ratio_value}% (raw)")
                self._check_value_change('ratio', self.ratio_value)
            else:
                print(
                    f"[DEBUG] Unexpected FEV1/FVC data format: {data.hex()} ({len(data)} bytes)")
        except Exception as e:
            print(f"Error decoding FEV1/FVC: {e}")

    def status_handler(self, sender, data):
        """Handle test status notifications."""
        try:
            # Always print status for debugging
            print(f"[DEBUG] Status raw data: {data.hex()} ({len(data)} bytes)")
            if len(data) >= 1:
                status = data[0]
                print(f"[DEBUG] Status value: {status}")
                if status == 2:  # Assuming 2 means test complete
                    self.test_complete = True
                    self.blow_end_time = time.time()
                    print("✅ Test complete!")
                else:
                    print(f"[DEBUG] Status {status} - test continuing")
            else:
                print("[DEBUG] Empty status data received")
        except Exception as e:
            print(f"Error decoding status: {e}")

    async def dump_all_characteristics(self):
        """Dump all readable characteristics for debugging."""
        print("\n" + "="*60)
        print("🔍 DUMPING ALL READABLE CHARACTERISTICS")
        print("="*60)

        for service in self.client.services:
            print(f"\nService: {service.uuid}")
            for char in service.characteristics:
                print(f"  Characteristic: {char.uuid}")
                print(f"  Properties: {char.properties}")

                if 'read' in char.properties:
                    try:
                        data = await self.client.read_gatt_char(char.uuid)
                        print(f"  Data: {data.hex()} ({len(data)} bytes)")

                        # Try to decode as different formats
                        if len(data) == 1:
                            print(f"    As byte: {data[0]}")
                        elif len(data) == 2:
                            val = struct.unpack('<H', data)[0]
                            print(f"    As uint16: {val}")
                        elif len(data) == 4:
                            try:
                                float_val = struct.unpack('<f', data)[0]
                                print(f"    As float: {float_val}")
                            except:
                                pass
                            try:
                                int_val = struct.unpack('<I', data)[0]
                                print(f"    As uint32: {int_val}")
                            except:
                                pass

                    except Exception as e:
                        print(f"  Error reading: {e}")
                else:
                    print(f"  Not readable")

        print("="*60)

    def calculate_predicted_pef(self):
        """Calculate predicted PEF using simplified Knudson equations."""
        if not all([self.age, self.height_cm, self.gender]):
            return None

        # Simplified PEF prediction based on Knudson equations
        # PEF in L/min
        if self.gender.lower() == 'male':
            # Male: PEF = 5.48 * height(m) + 0.0154 * age - 0.000226 * age^2 - 2.75
            height_m = self.height_cm / 100
            predicted_pef = (5.48 * height_m + 0.0154 * self.age -
                             # Convert to L/min
                             0.000226 * (self.age ** 2) - 2.75) * 60
        else:
            # Female: PEF = 3.72 * height(m) + 0.0103 * age - 0.000163 * age^2 - 1.11
            height_m = self.height_cm / 100
            predicted_pef = (3.72 * height_m + 0.0103 * self.age -
                             # Convert to L/min
                             0.000163 * (self.age ** 2) - 1.11) * 60

        return max(predicted_pef, 100)  # Minimum reasonable value

    def calculate_predicted_fev1(self):
        """Calculate predicted FEV1 using simplified Knudson equations."""
        if not all([self.age, self.height_cm, self.gender]):
            return None

        # Simplified FEV1 prediction based on Knudson equations
        # FEV1 in L
        height_m = self.height_cm / 100
        if self.gender.lower() == 'male':
            # Male: FEV1 = 4.30 * height(m) - 0.029 * age - 2.49
            predicted_fev1 = 4.30 * height_m - 0.029 * self.age - 2.49
        else:
            # Female: FEV1 = 3.95 * height(m) - 0.025 * age - 2.60
            predicted_fev1 = 3.95 * height_m - 0.025 * self.age - 2.60

        return max(predicted_fev1, 0.5)  # Minimum reasonable value

    def calculate_predicted_fvc(self):
        """Calculate predicted FVC using simplified Knudson equations."""
        if not all([self.age, self.height_cm, self.gender]):
            return None

        # Simplified FVC prediction based on Knudson equations
        # FVC in L
        height_m = self.height_cm / 100
        if self.gender.lower() == 'male':
            # Male: FVC = 5.76 * height(m) - 0.026 * age - 4.34
            predicted_fvc = 5.76 * height_m - 0.026 * self.age - 4.34
        else:
            # Female: FVC = 4.43 * height(m) - 0.026 * age - 2.89
            predicted_fvc = 4.43 * height_m - 0.026 * self.age - 2.89

        return max(predicted_fvc, 0.8)  # Minimum reasonable value

    def get_traffic_light_zone(self, actual_value, predicted_value):
        """Determine traffic light zone based on percentage of predicted value."""
        if predicted_value is None or predicted_value == 0:
            return "Unknown", 0

        percentage = (actual_value / predicted_value) * 100

        if percentage >= 80:
            return "Green", percentage
        elif percentage >= 50:
            return "Yellow", percentage
        else:
            return "Red", percentage

    def interpret_results(self):
        """Interpret spirometry results based on predicted values."""
        if not all([self.age, self.height_cm, self.gender]):
            return {
                'interpretation': 'Cannot interpret - missing demographic data',
                'zones': {}
            }

        # Calculate predicted values
        predicted_pef = self.calculate_predicted_pef()
        predicted_fev1 = self.calculate_predicted_fev1()
        predicted_fvc = self.calculate_predicted_fvc()

        # Convert PEF from L/s to L/min for comparison
        pef_l_min = self.pef_value * 60

        # Get traffic light zones
        pef_zone, pef_percent = self.get_traffic_light_zone(
            pef_l_min, predicted_pef)
        fev1_zone, fev1_percent = self.get_traffic_light_zone(
            self.fev1_value, predicted_fev1)
        fvc_zone, fvc_percent = self.get_traffic_light_zone(
            self.fvc_value, predicted_fvc)

        interpretation = {
            'predicted_values': {
                'PEF': round(predicted_pef, 1) if predicted_pef else None,
                'FEV1': round(predicted_fev1, 2) if predicted_fev1 else None,
                'FVC': round(predicted_fvc, 2) if predicted_fvc else None
            },
            'percentages': {
                'PEF': round(pef_percent, 1),
                'FEV1': round(fev1_percent, 1),
                'FVC': round(fvc_percent, 1)
            },
            'zones': {
                'PEF': pef_zone,
                'FEV1': fev1_zone,
                'FVC': fvc_zone
            },
            'overall_assessment': self._get_overall_assessment(pef_zone, fev1_zone, fvc_zone)
        }

        return interpretation

    def _get_overall_assessment(self, pef_zone, fev1_zone, fvc_zone):
        """Get overall assessment based on individual zones."""
        zones = [pef_zone, fev1_zone, fvc_zone]

        if 'Red' in zones:
            return 'Poor - Consult healthcare provider'
        elif 'Yellow' in zones:
            return 'Moderate - Monitor closely'
        elif all(zone == 'Green' for zone in zones):
            return 'Good - Within normal range'
        else:
            return 'Mixed results - Consult healthcare provider'

    async def setup_notifications(self):
        """Set up notifications for all relevant characteristics."""
        print("Setting up notifications...")

        try:
            await self.client.start_notify(self.fev1_char, self.fev1_handler)
            print("FEV1 notifications enabled")
        except Exception as e:
            print(f"Error enabling FEV1 notifications: {e}")

        try:
            await self.client.start_notify(self.fvc_char, self.fvc_handler)
            print("FVC notifications enabled")
        except Exception as e:
            print(f"Error enabling FVC notifications: {e}")

        try:
            await self.client.start_notify(self.pef_char, self.pef_handler)
            print("PEF notifications enabled")
        except Exception as e:
            print(f"Error enabling PEF notifications: {e}")

        try:
            await self.client.start_notify(self.fev1_fvc_char, self.ratio_handler)
            print("FEV1/FVC notifications enabled")
        except Exception as e:
            print(f"Error enabling FEV1/FVC notifications: {e}")

        try:
            await self.client.start_notify(self.status_char, self.status_handler)
            print("Status notifications enabled")
        except Exception as e:
            print(f"Error enabling status notifications: {e}")

    async def start_test(self):
        """Start a spirometry test."""
        print("Starting test...")
        self.test_complete = False

        # Command to start test
        try:
            await self.client.write_gatt_char(self.command_char, b'\x01')
            print("Start test command sent")
            return True
        except Exception as e:
            print(f"Error starting test: {e}")
            return False

    async def get_last_result(self):
        """Get the last test result."""
        print("Requesting last result...")

        try:
            await self.client.write_gatt_char(self.command_char, b'\x02')
            print("Get last result command sent")
            return True
        except Exception as e:
            print(f"Error getting last result: {e}")
            return False

    async def read_battery(self):
        """Read the battery level."""
        try:
            # Try standard battery service first
            data = await self.client.read_gatt_char(self.std_battery_char)
            if data:
                level = data[0]
                print(f"Battery level: {level}%")
                return level
        except Exception:
            try:
                # Try device-specific battery characteristic
                data = await self.client.read_gatt_char(self.battery_char)
                if data and len(data) >= 1:
                    level = data[0]
                    print(f"Battery level: {level}%")
                    return level
            except Exception as e:
                print(f"Error reading battery: {e}")

        return 0

    async def read_values_directly(self):
        """Read values directly from characteristics."""
        try:
            # Read FEV1
            data = await self.client.read_gatt_char(self.fev1_char)
            print(
                f"FEV1 direct read data: {data.hex() if data else 'None'}, length: {len(data) if data else 0}")
            if data:
                if len(data) == 4:
                    self.fev1_value = struct.unpack('<f', data)[0]
                    print(f"Read FEV1: {self.fev1_value} L")
                elif len(data) == 1:
                    self.fev1_value = data[0] / 100.0  # Assuming scale factor
                    print(f"Read FEV1 (single byte): {self.fev1_value} L")
                elif len(data) == 2:
                    # Handle 2-byte data format
                    if data.hex() == "ffff":
                        # This might be a placeholder or an error code
                        if self.fev1_value == 0:
                            # If we don't have a previous value, estimate based on FVC and ratio
                            if self.fvc_value > 0 and self.ratio_value > 0:
                                self.fev1_value = (
                                    self.fvc_value * self.ratio_value) / 100.0
                                print(
                                    f"FEV1 estimated from FVC and ratio: {self.fev1_value} L")
                            else:
                                # Default to a small value if we can't estimate
                                self.fev1_value = 0.05
                                print(
                                    f"FEV1 set to default value: {self.fev1_value} L")
                    else:
                        # Try to interpret as a 16-bit integer
                        try:
                            value = struct.unpack('<H', data)[0]
                            self.fev1_value = value / 100.0  # Assuming scale factor
                            print(f"Read FEV1 (16-bit): {self.fev1_value} L")
                        except:
                            print(
                                f"Could not decode 2-byte FEV1 data: {data.hex()}")
                else:
                    print(f"Unexpected FEV1 data format: {data.hex()}")

            # Read FVC
            data = await self.client.read_gatt_char(self.fvc_char)
            print(
                f"FVC direct read data: {data.hex() if data else 'None'}, length: {len(data) if data else 0}")
            if data:
                if len(data) == 4:
                    self.fvc_value = struct.unpack('<f', data)[0]
                    print(f"Read FVC: {self.fvc_value} L")
                elif len(data) == 1:
                    self.fvc_value = data[0] / 100.0  # Assuming scale factor
                    print(f"Read FVC (single byte): {self.fvc_value} L")
                else:
                    print(f"Unexpected FVC data format: {data.hex()}")

            # Read PEF
            data = await self.client.read_gatt_char(self.pef_char)
            print(
                f"PEF direct read data: {data.hex() if data else 'None'}, length: {len(data) if data else 0}")
            if data:
                if len(data) == 4:
                    self.pef_value = struct.unpack('<f', data)[0]
                    print(f"Read PEF: {self.pef_value} L/s")
                elif len(data) == 1:
                    self.pef_value = data[0] / 10.0  # Assuming scale factor
                    print(f"Read PEF (single byte): {self.pef_value} L/s")
                else:
                    print(f"Unexpected PEF data format: {data.hex()}")

            # Read FEV1/FVC
            data = await self.client.read_gatt_char(self.fev1_fvc_char)
            print(
                f"FEV1/FVC direct read data: {data.hex() if data else 'None'}, length: {len(data) if data else 0}")
            if data:
                if len(data) == 4:
                    self.ratio_value = struct.unpack('<f', data)[0]
                    print(f"Read FEV1/FVC: {self.ratio_value}%")
                elif len(data) == 1:
                    self.ratio_value = data[0]  # Assuming percentage
                    print(f"Read FEV1/FVC (single byte): {self.ratio_value}%")
                else:
                    print(f"Unexpected FEV1/FVC data format: {data.hex()}")

        except Exception as e:
            print(f"Error reading values: {e}")

    def is_test_stable(self, stability_duration=3):
        """Check if test values have been stable for the specified duration."""
        if self.last_value_update is None:
            return False

        time_since_update = time.time() - self.last_value_update
        is_stable = time_since_update >= stability_duration and self.test_started

        # Set blow end time when test becomes stable
        if is_stable and self.blow_end_time is None:
            self.blow_end_time = time.time()

        return is_stable

    def get_blow_duration(self):
        """Get the duration of the blow in seconds."""
        if self.blow_start_time is None:
            return None

        end_time = self.blow_end_time or time.time()
        return end_time - self.blow_start_time

    async def get_results(self):
        """Get the test results with interpretation."""
        # Read values directly in case notifications didn't work
        await self.read_values_directly()

        results = {
            'PEF': round(self.pef_value, 2),
            'FEV1': round(self.fev1_value, 2),
            'FVC': round(self.fvc_value, 2),
            'FEV1/FVC': round(self.ratio_value, 2)
        }

        # Add blow duration if available
        duration = self.get_blow_duration()
        if duration is not None:
            results['blow_duration'] = round(duration, 2)

        # Add interpretation if demographics are available
        if all([self.age, self.height_cm, self.gender]):
            results['interpretation'] = self.interpret_results()

        return results

    async def disconnect(self):
        """Disconnect from the device."""
        if self.client and self.client.is_connected:
            # Try to stop notifications
            try:
                await self.client.stop_notify(self.fev1_char)
                await self.client.stop_notify(self.fvc_char)
                await self.client.stop_notify(self.pef_char)
                await self.client.stop_notify(self.fev1_fvc_char)
                await self.client.stop_notify(self.status_char)
            except Exception as e:
                print(f"Error stopping notifications: {e}")
                pass

            try:
                await self.client.disconnect()
                print("Disconnected from MIR Smart One")
            except EOFError:
                # This can happen if the device disconnects on its own
                print("Device appears to have already disconnected")
            except Exception as e:
                print(f"Error during disconnect: {e}")
                # The connection might already be closed


async def perform_spirometry_test_bluetooth(age=None, height_cm=None, gender=None, weight_kg=None, debug=True):
    """Perform a spirometry test using the MIR Smart One device with dynamic detection."""
    spirometer = None
    try:
        print("Initializing MIR Smart One spirometer...")
        spirometer = MirSmartOne(
            age=age, height_cm=height_cm, gender=gender, weight_kg=weight_kg, debug=debug)
        await spirometer.connect()

        # Dump all characteristics for debugging
        await spirometer.dump_all_characteristics()

        # Set up notifications
        await spirometer.setup_notifications()

        # Check battery
        await spirometer.read_battery()

        # First try to get the last result
        print("\nChecking for previous test results...")
        await spirometer.get_last_result()

        # Wait a bit for notifications
        print("Waiting for previous result data...")
        await asyncio.sleep(3)

        # Try to read values directly to see if we got any previous results
        await spirometer.read_values_directly()

        # If we want to perform a new test
        print("\n" + "="*60)
        print("📢 SPIROMETRY TEST INSTRUCTIONS")
        print("="*60)
        print("🔹 Stand or sit upright with good posture")
        print("🔹 Take the deepest breath you can")
        print("🔹 Seal your lips tightly around the mouthpiece")
        print("🔹 Blow out as HARD and FAST as you can")
        print("🔹 Continue blowing until your lungs are completely empty")
        print("🔹 Minimum blow time should be 1 second for accurate FEV1")
        print("="*60)

        input("Press Enter when you're ready to begin the test...")

        print("\n🫁 Prepare to blow...")
        await asyncio.sleep(2)
        print("💨 Blow NOW! as hard and fast as you can...")

        # Start the test
        await spirometer.start_test()

        # Dynamic test detection instead of fixed timeout
        print("Performing measurement...")
        max_timeout = 30  # Maximum safety timeout
        start_time = time.time()

        # Wait for test to start (values to begin changing)
        print("Waiting for test to start...")
        while not spirometer.test_started and time.time() - start_time < 10:
            await asyncio.sleep(0.5)

        if not spirometer.test_started:
            print("Warning: Test may not have started properly")
        else:
            print("Test started! Monitoring for completion...")

        # Wait for test completion using dynamic detection
        while (not spirometer.test_complete and
               not spirometer.is_test_stable() and
               time.time() - start_time < max_timeout):
            await asyncio.sleep(1)

            # Show progress
            if spirometer.test_started:
                time_since_update = time.time(
                ) - spirometer.last_value_update if spirometer.last_value_update else 0
                print(
                    f"Test in progress... (stable for {time_since_update:.1f}s)")
            else:
                print("Waiting for test to start...")

        # Determine completion reason
        if spirometer.test_complete:
            print("Test completed via status notification!")
        elif spirometer.is_test_stable():
            print("Test completed via value stabilization!")
        else:
            print("Test timed out - using available data")

        # Give a moment for any final notifications
        print("Collecting final data...")
        await asyncio.sleep(2)

        # Get the results
        print("Reading final results...")
        results = await spirometer.get_results()

        # Disconnect
        await spirometer.disconnect()

        return results
    except Exception as e:
        print(f"Error during Bluetooth spirometry test: {e}")
        import traceback
        traceback.print_exc()
        return None
    finally:
        # Make sure we disconnect even if there was an error
        if spirometer and spirometer.client and spirometer.client.is_connected:
            try:
                await spirometer.disconnect()
            except Exception as e:
                print(f"Error during disconnect: {e}")


def get_patient_demographics():
    """Prompt user for demographic information."""
    print("\n" + "="*60)
    print("👤 PATIENT DEMOGRAPHIC INFORMATION")
    print("="*60)
    print("For accurate result interpretation, please provide:")
    print("(Press Enter to skip any field)")
    print()

    try:
        age_input = input("Age (years): ").strip()
        age = int(age_input) if age_input else None

        height_input = input("Height (cm): ").strip()
        height_cm = float(height_input) if height_input else None

        while True:
            gender_input = input("Gender (male/female): ").strip().lower()
            if not gender_input:
                gender = None
                break
            elif gender_input in ['male', 'female', 'm', 'f']:
                gender = 'male' if gender_input.startswith('m') else 'female'
                break
            else:
                print("Please enter 'male' or 'female' (or press Enter to skip)")

        weight_input = input("Weight (kg): ").strip()
        weight_kg = float(weight_input) if weight_input else None

        return age, height_cm, gender, weight_kg

    except ValueError:
        print("⚠️  Invalid input detected. Using default values for demonstration.")
        return 45, 170, 'male', 75


if __name__ == "__main__":
    try:
        print("🫁 Enhanced MIR Smart One Spirometry Test")
        print("="*60)

        # Get patient demographics
        age, height_cm, gender, weight_kg = get_patient_demographics()

        if all([age, height_cm, gender]):
            print(f"\n✅ Using demographics: {age}y, {height_cm}cm, {gender}")
            if weight_kg:
                print(f"   Weight: {weight_kg}kg")
        else:
            print("\n⚠️  Limited demographic data - basic results only")

        results = asyncio.run(perform_spirometry_test_bluetooth(
            age=age, height_cm=height_cm, gender=gender, weight_kg=weight_kg))

        if results:
            print("\n" + "="*60)
            print("🫁 SPIROMETRY RESULTS")
            print("="*60)
            print(f"📊 PEF (Peak Expiratory Flow): {results['PEF']} L/s")
            print(f"📊 FEV1 (Forced Expiratory Volume): {results['FEV1']} L")
            print(f"📊 FVC (Forced Vital Capacity): {results['FVC']} L")
            print(f"📊 FEV1/FVC Ratio: {results['FEV1/FVC']}%")

            # Show blow duration if available
            if 'blow_duration' in results:
                duration = results['blow_duration']
                duration_status = "✅ Good" if duration >= 1.0 else "⚠️  Short"
                print(f"⏱️  Blow Duration: {duration}s ({duration_status})")
                if duration < 1.0:
                    print(
                        "   💡 Tip: Try to blow for at least 1 second for accurate FEV1")

            # Show interpretation if available
            if 'interpretation' in results:
                interp = results['interpretation']
                print("\n" + "="*50)
                print("INTERPRETATION")
                print("="*50)

                if 'predicted_values' in interp:
                    pred = interp['predicted_values']
                    print("Predicted Values:")
                    print(f"  PEF: {pred['PEF']} L/min")
                    print(f"  FEV1: {pred['FEV1']} L")
                    print(f"  FVC: {pred['FVC']} L")

                if 'percentages' in interp:
                    perc = interp['percentages']
                    print("\nPercentage of Predicted:")
                    print(f"  PEF: {perc['PEF']}%")
                    print(f"  FEV1: {perc['FEV1']}%")
                    print(f"  FVC: {perc['FVC']}%")

                if 'zones' in interp:
                    zones = interp['zones']
                    print("\nTraffic Light Zones:")
                    print(f"  PEF: {zones['PEF']}")
                    print(f"  FEV1: {zones['FEV1']}")
                    print(f"  FVC: {zones['FVC']}")

                if 'overall_assessment' in interp:
                    print(
                        f"\nOverall Assessment: {interp['overall_assessment']}")

                print("\nZone Legend:")
                print("  🟢 Green (≥80%): Good - Within normal range")
                print("  🟡 Yellow (50-79%): Moderate - Monitor closely")
                print("  🔴 Red (<50%): Poor - Consult healthcare provider")
        else:
            print("Test failed or no results obtained.")

    except KeyboardInterrupt:
        print("\nTest cancelled.")
